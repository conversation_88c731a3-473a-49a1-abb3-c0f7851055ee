// 导入日志系统模块
mod rizhixitong;
// 导入初始化模块
mod chushihua;
// 导入服务器模块
mod fuwuqi;
use chushihua::houduan_chushihua::houduan_chushihua;
use std::env;

// 异步主函数入口点
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 解析命令行参数
    let args: Vec<String> = env::args().collect();

    if args.len() > 1 {
        match args[1].as_str() {
            "init" => {
                // 初始化模式
                match houduan_chushihua::houduan_chushihua_jiandan().await {
                    Ok(_) => {
                        println!("✅ 系统初始化成功！");
                        std::process::exit(0);
                    }
                    Err(e) => {
                        eprintln!("❌ 系统初始化失败: {}", e);
                        std::process::exit(1);
                    }
                }
            }
            "test" => {
                // 测试模式
                match houduan_chushihua::yunxing_wupin_shuju_ceshi().await {
                    Ok(_) => {
                        println!("✅ 物品数据测试完成！");
                        std::process::exit(0);
                    }
                    Err(e) => {
                        eprintln!("❌ 物品数据测试失败: {}", e);
                        std::process::exit(1);
                    }
                }
            }
            _ => {
                println!("未知参数: {}", args[1]);
                println!("可用参数:");
                println!("  init - 初始化系统");
                println!("  test - 运行物品数据测试");
                std::process::exit(1);
            }
        }
    }

    // 默认启动后端服务器
    houduan_chushihua::qidong_fuwuqi().await?;
    Ok(())
}
