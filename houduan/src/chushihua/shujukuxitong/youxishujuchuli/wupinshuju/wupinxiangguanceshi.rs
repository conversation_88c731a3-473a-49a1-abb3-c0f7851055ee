#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::wupin_mingzi_chaxun::{ming<PERSON>_chaxun_tiaojian, wupin_ming<PERSON>_chaxun_guanli};
use super::wupin_fenleiliebiao_chaxun::{fenlei_liebiao_chaxun_tiaojian, wupin_fenlei_liebiao_chaxun_guanli};
use super::wupin_redis_kongzhi::wupin_redis_kongzhi;
use super::wupinjiegouti::fenye_canshu;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use anyhow::Result;

/// 物品列表查询相关测试类
pub struct wupin_xiangguan<PERSON>hi;

impl wupin_xiangguanceshi {
    /// 测试物品名字查询功能
    pub async fn ceshi_wupin_mingzi_chaxun(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: Option<redis_lianjie_guanli>,
    ) -> Result<()> {
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "开始测试物品名字查询功能...",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        // 创建查询管理器
        let mingzi_chaxun_guanli = if let Some(ref redis) = redis_lianjie {
            wupin_mingzi_chaxun_guanli::new_with_redis(mysql_lianjie, redis.clone())
        } else {
            wupin_mingzi_chaxun_guanli::new(mysql_lianjie)
        };

        // 创建分页参数
        let fenye_canshu = fenye_canshu::new(1, 10);

        // 测试1: 模糊名字查询 - "杰勒"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试1: 模糊名字查询 - '杰勒'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        let mohu_chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_mohu(
            "杰勒".to_string(),
            fenye_canshu.clone(),
        );

        match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&mohu_chaxun_tiaojian).await {
            Ok(jieguo) => {
                // 显示数据来源
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("模糊查询'杰勒' - 数据来源: {}", laiyuan),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                // 显示原始JSON内容
                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("模糊查询'杰勒'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("模糊查询'杰勒'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 测试2: 精确名字查询 - "杰勒比结晶"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试2: 精确名字查询 - '杰勒比结晶'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        let jingque_chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_jingque(
            "杰勒比结晶".to_string(),
            fenye_canshu.clone(),
        );

        match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&jingque_chaxun_tiaojian).await {
            Ok(jieguo) => {
                // 显示数据来源
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("精确查询'杰勒比结晶' - 数据来源: {}", laiyuan),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                // 显示原始JSON内容
                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("精确查询'杰勒比结晶'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("精确查询'杰勒比结晶'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 测试3: 类名精确查询 - "Jellopy"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试3: 类名精确查询 - 'Jellopy'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        let leiming_chaxun_tiaojian = mingzi_chaxun_tiaojian::leiming_jingque(
            "Jellopy".to_string(),
            fenye_canshu.clone(),
        );

        match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&leiming_chaxun_tiaojian).await {
            Ok(jieguo) => {
                // 显示数据来源
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("类名查询'Jellopy' - 数据来源: {}", laiyuan),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                // 显示原始JSON内容
                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("类名查询'Jellopy'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("类名查询'Jellopy'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "物品名字查询功能测试完成！",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        // 测试完成后清除缓存
        if let Some(redis) = redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis);
            if let Err(e) = redis_kongzhi.qingchu_ceshi_huancun().await {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除测试缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        Ok(())
    }

    /// 测试物品分类列表查询功能
    pub async fn ceshi_wupin_fenlei_liebiao_chaxun(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: Option<redis_lianjie_guanli>,
    ) -> Result<()> {
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "开始测试物品分类列表查询功能...",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        // 创建查询管理器
        let fenlei_liebiao_guanli = if let Some(ref redis) = redis_lianjie {
            wupin_fenlei_liebiao_chaxun_guanli::new_with_redis(mysql_lianjie, redis.clone())
        } else {
            wupin_fenlei_liebiao_chaxun_guanli::new(mysql_lianjie)
        };

        // 创建分页参数
        let fenye_canshu = fenye_canshu::new(1, 10);

        // 测试1: 按类型查询 - "Weapon"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试1: 按类型查询 - 'Weapon'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        match fenlei_liebiao_guanli.chaxun_by_leixing("Weapon", &fenye_canshu).await {
            Ok(jieguo) => {
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("按类型查询'Weapon' - 数据来源: {}, 总数: {}", laiyuan, jieguo.zong_shuliang),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("按类型查询'Weapon'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("按类型查询'Weapon'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 测试2: 按类型和子类型查询 - "Weapon" + "Sword"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试2: 按类型和子类型查询 - 'Weapon' + 'Sword'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        match fenlei_liebiao_guanli.chaxun_by_leixing_zileixing("Weapon", "Sword", &fenye_canshu).await {
            Ok(jieguo) => {
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("按类型和子类型查询'Weapon'+'Sword' - 数据来源: {}, 总数: {}", laiyuan, jieguo.zong_shuliang),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("按类型和子类型查询'Weapon'+'Sword'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("按类型和子类型查询'Weapon'+'Sword'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 测试3: 联合查询 - "Weapon" + "Sword" + 模糊名字查询"剑"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试3: 联合查询 - 'Weapon' + 'Sword' + 模糊名字查询'剑'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        let mingzi_tiaojian = mingzi_chaxun_tiaojian::mingzi_mohu(
            "剑".to_string(),
            fenye_canshu.clone(),
        );

        match fenlei_liebiao_guanli.lianhe_chaxun("Weapon", "Sword", &mingzi_tiaojian, &fenye_canshu).await {
            Ok(jieguo) => {
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("联合查询'Weapon'+'Sword'+'剑' - 数据来源: {}, 总数: {}", laiyuan, jieguo.zong_shuliang),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("联合查询'Weapon'+'Sword'+'剑'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("联合查询'Weapon'+'Sword'+'剑'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 测试4: 按子类型查询（不限制类型） - "Sword"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试4: 按子类型查询（不限制类型） - 'Sword'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        match fenlei_liebiao_guanli.chaxun_by_zileixing("Sword", &fenye_canshu).await {
            Ok(jieguo) => {
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("按子类型查询'Sword' - 数据来源: {}, 总数: {}", laiyuan, jieguo.zong_shuliang),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );

                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("按子类型查询'Sword'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("按子类型查询'Sword'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 测试5: 获取统计信息
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试5: 获取统计信息",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        match fenlei_liebiao_guanli.huoqu_leixing_tongji("Weapon").await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("'Weapon'类型物品总数: {}", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("获取'Weapon'类型统计失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        match fenlei_liebiao_guanli.huoqu_leixing_zileixing_tongji("Weapon", "Sword").await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("'Weapon'+'Sword'类型物品总数: {}", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("获取'Weapon'+'Sword'类型统计失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "物品分类列表查询功能测试完成！",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        // 测试完成后清除缓存
        if let Some(redis) = redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis);
            if let Err(e) = redis_kongzhi.qingchu_ceshi_huancun().await {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除测试缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        Ok(())
    }
}