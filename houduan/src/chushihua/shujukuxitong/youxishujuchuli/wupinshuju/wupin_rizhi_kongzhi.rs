#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 物品数据处理字符串常量管理类
pub struct wupin_zifuchuan_changliangguanli;

impl wupin_zifuchuan_changliangguanli {
    // ==================== 查询模式常量 ====================

    /// 全部信息查询模式
    pub const chaxun_moshi_quanbu_xinxi: &'static str = "quanbu_xinxi";

    // ==================== 错误信息常量 ====================

    /// 物品不存在错误信息模板
    pub const cuowu_wupin_bucunzai: &'static str = "物品ID {} 不存在";

    /// 指定字段不在汇总表中错误信息
    pub const cuowu_ziduan_bu_zai_huizong_biao: &'static str = "指定的字段都不在汇总表中";

    /// 物品在汇总表中不存在错误信息模板
    pub const cuowu_wupin_zai_huizong_biao_bucunzai: &'static str = "物品ID {} 在汇总表中不存在";

    /// 物品基础信息不存在错误信息
    pub const cuowu_wupin_jiben_xinxi_bucunzai: &'static str = "物品基础信息不存在";

    /// 物品汇总信息不存在错误信息
    pub const cuowu_wupin_huizong_xinxi_bucunzai: &'static str = "物品汇总信息不存在";

    // ==================== Redis相关常量 ====================

    /// Redis键前缀 - 物品全部信息
    pub const redis_jian_qianzhui_wupin_quanbu: &'static str = "wupin_quanbu";

    /// Redis键模式 - 物品全部信息（用于批量删除）
    pub const redis_jian_moshi_wupin_quanbu: &'static str = "wupin_quanbu:*";

    /// Redis键前缀 - 物品分类类型列表
    pub const redis_jian_qianzhui_fenlei_leixing: &'static str = "wupin_fenlei_leixing";

    /// Redis键前缀 - 物品分类子类型列表
    pub const redis_jian_qianzhui_fenlei_zileixing: &'static str = "wupin_fenlei_zileixing";

    /// Redis键前缀 - 物品完整分类信息
    pub const redis_jian_qianzhui_fenlei_wanzheng: &'static str = "wupin_fenlei_wanzheng";

    /// Redis键模式 - 物品分类相关（用于批量删除）
    pub const redis_jian_moshi_fenlei_suoyou: &'static str = "wupin_fenlei_*";

    /// Redis键前缀 - 物品列表分页
    pub const redis_jian_qianzhui_liebiao_fenye: &'static str = "wupin_liebiao_fenye";

    /// Redis键模式 - 物品列表相关（用于批量删除）
    pub const redis_jian_moshi_liebiao_suoyou: &'static str = "wupin_liebiao_*";

    /// Redis键前缀 - 物品名字查询
    pub const redis_jian_qianzhui_mingzi_chaxun: &'static str = "wupin_mingzi_chaxun";

    /// Redis键模式 - 物品名字查询相关（用于批量删除）
    pub const redis_jian_moshi_mingzi_chaxun_suoyou: &'static str = "wupin_mingzi_chaxun_*";

    /// Redis键前缀 - 物品分类列表查询
    pub const redis_jian_qianzhui_fenlei_liebiao: &'static str = "wupin_fenlei_liebiao";

    /// Redis键模式 - 物品分类列表查询相关（用于批量删除）
    pub const redis_jian_moshi_fenlei_liebiao_suoyou: &'static str = "wupin_fenlei_liebiao:*";

    /// 分类缓存时间：30分钟（1800秒）
    pub const fenlei_huancun_shijian: u64 = 1800;

    /// 分类列表查询缓存时间：5小时（18000秒）
    pub const fenlei_liebiao_huancun_shijian: u64 = 18000;

    /// 列表缓存时间：5小时（18000秒）
    pub const liebiao_huancun_shijian: u64 = 18000;

    /// 名字查询缓存时间：5小时（18000秒）
    pub const mingzi_chaxun_huancun_shijian: u64 = 18000;

    // ==================== 统计信息常量 ====================

    /// 物品全部信息缓存统计描述
    pub const tongji_wupin_quanbu_xinxi_huancun: &'static str = "物品全部信息缓存: {} 个";

    /// 物品全部信息缓存获取失败描述
    pub const tongji_wupin_quanbu_xinxi_huancun_shibai: &'static str = "物品全部信息缓存: 获取失败";

    /// 未启用Redis缓存描述
    pub const tongji_wei_qiyong_redis_huancun: &'static str = "未启用Redis缓存";

    /// 物品分类类型列表缓存统计描述
    pub const tongji_fenlei_leixing_huancun: &'static str = "物品分类类型列表缓存: {} 个";

    /// 物品分类子类型列表缓存统计描述
    pub const tongji_fenlei_zileixing_huancun: &'static str = "物品分类子类型列表缓存: {} 个";

    /// 物品完整分类信息缓存统计描述
    pub const tongji_fenlei_wanzheng_huancun: &'static str = "物品完整分类信息缓存: {} 个";

    /// 分类缓存清除成功描述
    pub const tongji_fenlei_huancun_qingchu_chenggong: &'static str = "物品分类缓存清除成功";

    /// 分类缓存清除失败描述
    pub const tongji_fenlei_huancun_qingchu_shibai: &'static str = "物品分类缓存清除失败: {}";

    /// 物品列表分页缓存统计描述
    pub const tongji_liebiao_fenye_huancun: &'static str = "物品列表分页缓存: {} 个";

    /// 列表缓存清除成功描述
    pub const tongji_liebiao_huancun_qingchu_chenggong: &'static str = "物品列表缓存清除成功";

    /// 列表缓存清除失败描述
    pub const tongji_liebiao_huancun_qingchu_shibai: &'static str = "物品列表缓存清除失败: {}";

    /// 物品分类类型列表缓存已缓存描述
    pub const tongji_fenlei_leixing_yi_huancun: &'static str = "物品分类类型列表缓存: 已缓存";

    /// 物品分类类型列表缓存未缓存描述
    pub const tongji_fenlei_leixing_wei_huancun: &'static str = "物品分类类型列表缓存: 未缓存";

    /// 物品分类子类型列表缓存获取失败描述
    pub const tongji_fenlei_zileixing_huoqu_shibai: &'static str = "物品分类子类型列表缓存: 获取失败";

    /// 物品完整分类信息缓存已缓存描述
    pub const tongji_fenlei_wanzheng_yi_huancun: &'static str = "物品完整分类信息缓存: 已缓存";

    /// 物品完整分类信息缓存未缓存描述
    pub const tongji_fenlei_wanzheng_wei_huancun: &'static str = "物品完整分类信息缓存: 未缓存";

    /// 物品列表分页缓存获取失败描述
    pub const tongji_liebiao_fenye_huoqu_shibai: &'static str = "物品列表分页缓存: 获取失败";

    /// 物品名字查询缓存统计描述
    pub const tongji_mingzi_chaxun_huancun: &'static str = "物品名字查询缓存: {} 个";

    /// 名字查询缓存清除成功描述
    pub const tongji_mingzi_chaxun_huancun_qingchu_chenggong: &'static str = "物品名字查询缓存清除成功";

    /// 名字查询缓存清除失败描述
    pub const tongji_mingzi_chaxun_huancun_qingchu_shibai: &'static str = "物品名字查询缓存清除失败: {}";

    /// 物品名字查询缓存获取失败描述
    pub const tongji_mingzi_chaxun_huoqu_shibai: &'static str = "物品名字查询缓存: 获取失败";

    /// 物品分类列表查询缓存统计描述
    pub const tongji_fenlei_liebiao_huancun: &'static str = "物品分类列表查询缓存: {} 个";

    /// 分类列表查询缓存清除成功描述
    pub const tongji_fenlei_liebiao_huancun_qingchu_chenggong: &'static str = "物品分类列表查询缓存清除成功";

    /// 分类列表查询缓存清除失败描述
    pub const tongji_fenlei_liebiao_huancun_qingchu_shibai: &'static str = "物品分类列表查询缓存清除失败: {}";

    /// 物品分类列表查询缓存获取失败描述
    pub const tongji_fenlei_liebiao_huoqu_shibai: &'static str = "物品分类列表查询缓存: 获取失败";

    // ==================== SQL查询相关常量 ====================

    /// item_name表查询基础信息SQL
    pub const sql_chaxun_jiben_xinxi: &'static str = "SELECT ID, zhognwenming, zhognwenjieshao FROM item_name WHERE ID = ?";

    /// wupin_huizong表查询汇总信息SQL
    pub const sql_chaxun_huizong_xinxi: &'static str = r#"
        SELECT wupin_id, wupin_mingcheng, zhongwenming, wanzhengxing_fenshu,
               leixing, zileixing, zhongliang, guaiwu_shuliang, shangjia_shuliang,
               chongwu_shuliang, xiangzi_shuliang, lishi_jilu_shuliang,
               jichuxinxi_yaml, guaiwulaiyuan_yaml, shoumaishangjia_yaml,
               chongwu_yaml, laiyuanxiangzi_yaml, lishibiandong_yaml,
               you_jichuxinxi, you_guaiwulaiyuan, you_shoumaishangjia,
               you_chongwu, you_laiyuanxiangzi, you_lishibiandong
        FROM wupin_huizong WHERE wupin_id = ?
    "#;

    /// 检查物品存在性SQL - item_name表
    pub const sql_jiancha_jiben_biao_cunzai: &'static str = "SELECT COUNT(*) as count FROM item_name WHERE ID = ?";

    /// 检查物品存在性SQL - wupin_huizong表
    pub const sql_jiancha_huizong_biao_cunzai: &'static str = "SELECT COUNT(*) as count FROM wupin_huizong WHERE wupin_id = ?";

    /// 查询所有物品类型SQL
    pub const sql_chaxun_suoyou_leixing: &'static str = "SELECT DISTINCT leixing FROM wupin_huizong WHERE leixing IS NOT NULL AND leixing != '' ORDER BY leixing";

    /// 根据类型查询子类型SQL
    pub const sql_chaxun_zileixing_by_leixing: &'static str = "SELECT DISTINCT zileixing FROM wupin_huizong WHERE leixing = ? AND zileixing IS NOT NULL AND zileixing != '' ORDER BY zileixing";

    /// 查询完整分类信息SQL
    pub const sql_chaxun_wanzheng_fenlei: &'static str = r#"
        SELECT leixing, zileixing
        FROM wupin_huizong
        WHERE leixing IS NOT NULL AND leixing != ''
        AND zileixing IS NOT NULL AND zileixing != ''
        ORDER BY leixing, zileixing
    "#;

    /// 查询分类统计信息SQL
    pub const sql_chaxun_fenlei_tongji: &'static str = r#"
        SELECT
            leixing,
            COUNT(*) as shuliang
        FROM wupin_huizong
        WHERE leixing IS NOT NULL AND leixing != ''
        GROUP BY leixing
        ORDER BY shuliang DESC, leixing
    "#;

    /// 查询物品列表SQL（带分页）
    pub const sql_chaxun_liebiao_fenye: &'static str = r#"
        SELECT
            h.wupin_id,
            COALESCE(n.zhognwenming, h.zhongwenming) as wupin_mingcheng,
            h.leixing,
            h.zileixing
        FROM wupin_huizong h
        LEFT JOIN item_name n ON h.wupin_id = n.ID
        WHERE h.wupin_id IS NOT NULL AND h.wupin_id != ''
        ORDER BY h.wupin_id
        LIMIT ? OFFSET ?
    "#;

    /// 查询物品总数SQL
    pub const sql_chaxun_liebiao_zongshu: &'static str = r#"
        SELECT COUNT(*) as zong_shuliang
        FROM wupin_huizong
        WHERE wupin_id IS NOT NULL AND wupin_id != ''
    "#;

    /// 根据类名精确查询物品列表SQL（带分页）
    pub const sql_chaxun_by_leiming_jingque: &'static str = r#"
        SELECT
            h.wupin_id,
            COALESCE(n.zhognwenming, h.zhongwenming) as wupin_mingcheng,
            h.leixing,
            h.zileixing,
            h.leiming
        FROM wupin_huizong h
        LEFT JOIN item_name n ON h.wupin_id = n.ID
        WHERE h.leiming = ?
        ORDER BY h.wupin_id
        LIMIT ? OFFSET ?
    "#;

    /// 根据类名精确查询物品总数SQL
    pub const sql_chaxun_by_leiming_jingque_zongshu: &'static str = r#"
        SELECT COUNT(*) as zong_shuliang
        FROM wupin_huizong
        WHERE leiming = ?
    "#;

    /// 根据名字精确查询物品列表SQL（带分页）
    pub const sql_chaxun_by_mingzi_jingque: &'static str = r#"
        SELECT DISTINCT
            h.wupin_id,
            COALESCE(n.zhognwenming, h.zhongwenming) as wupin_mingcheng,
            h.leixing,
            h.zileixing,
            h.leiming
        FROM wupin_huizong h
        LEFT JOIN item_name n ON h.wupin_id = n.ID
        WHERE (n.zhognwenming = ? OR h.zhongwenming = ?)
        AND h.wupin_id IS NOT NULL AND h.wupin_id != ''
        ORDER BY h.wupin_id
        LIMIT ? OFFSET ?
    "#;

    /// 根据名字精确查询物品总数SQL
    pub const sql_chaxun_by_mingzi_jingque_zongshu: &'static str = r#"
        SELECT COUNT(DISTINCT h.wupin_id) as zong_shuliang
        FROM wupin_huizong h
        LEFT JOIN item_name n ON h.wupin_id = n.ID
        WHERE (n.zhognwenming = ? OR h.zhongwenming = ?)
        AND h.wupin_id IS NOT NULL AND h.wupin_id != ''
    "#;

    /// 根据名字模糊查询物品列表SQL（带分页）
    pub const sql_chaxun_by_mingzi_mohu: &'static str = r#"
        SELECT DISTINCT
            h.wupin_id,
            COALESCE(n.zhognwenming, h.zhongwenming) as wupin_mingcheng,
            h.leixing,
            h.zileixing,
            h.leiming
        FROM wupin_huizong h
        LEFT JOIN item_name n ON h.wupin_id = n.ID
        WHERE (n.zhognwenming LIKE ? OR h.zhongwenming LIKE ?)
        AND h.wupin_id IS NOT NULL AND h.wupin_id != ''
        ORDER BY h.wupin_id
        LIMIT ? OFFSET ?
    "#;

    /// 根据名字模糊查询物品总数SQL
    pub const sql_chaxun_by_mingzi_mohu_zongshu: &'static str = r#"
        SELECT COUNT(DISTINCT h.wupin_id) as zong_shuliang
        FROM wupin_huizong h
        LEFT JOIN item_name n ON h.wupin_id = n.ID
        WHERE (n.zhognwenming LIKE ? OR h.zhongwenming LIKE ?)
        AND h.wupin_id IS NOT NULL AND h.wupin_id != ''
    "#;

    // ==================== 分类输出错误信息常量 ====================

    /// 查询类型列表失败错误信息模板
    pub const cuowu_chaxun_leixing_liebiao_shibai: &'static str = "查询类型列表失败: {}";

    /// 查询子类型列表失败错误信息模板
    pub const cuowu_chaxun_zileixing_liebiao_shibai: &'static str = "查询子类型列表失败: {}";

    /// 查询完整分类信息失败错误信息模板
    pub const cuowu_chaxun_wanzheng_fenlei_shibai: &'static str = "查询完整分类信息失败: {}";

    /// 查询分类统计信息失败错误信息模板
    pub const cuowu_chaxun_fenlei_tongji_shibai: &'static str = "查询分类统计信息失败: {}";

    /// 查询物品列表失败错误信息模板
    pub const cuowu_chaxun_liebiao_shibai: &'static str = "查询物品列表失败: {}";

    /// 查询物品总数失败错误信息模板
    pub const cuowu_chaxun_zongshu_shibai: &'static str = "查询物品总数失败: {}";

    /// 清除分类缓存失败错误信息模板
    pub const cuowu_qingchu_fenlei_huancun_shibai: &'static str = "清除分类缓存失败: {}";

    /// 清除列表缓存失败错误信息模板
    pub const cuowu_qingchu_liebiao_huancun_shibai: &'static str = "清除列表缓存失败: {}";

    /// 根据类名查询失败错误信息模板
    pub const cuowu_chaxun_by_leiming_shibai: &'static str = "根据类名查询失败: {}";

    /// 根据名字精确查询失败错误信息模板
    pub const cuowu_chaxun_by_mingzi_jingque_shibai: &'static str = "根据名字精确查询失败: {}";

    /// 根据名字模糊查询失败错误信息模板
    pub const cuowu_chaxun_by_mingzi_mohu_shibai: &'static str = "根据名字模糊查询失败: {}";

    /// 清除名字查询缓存失败错误信息模板
    pub const cuowu_qingchu_mingzi_chaxun_huancun_shibai: &'static str = "清除名字查询缓存失败: {}";

    /// 分类列表查询失败错误信息模板
    pub const cuowu_fenlei_liebiao_chaxun_shibai: &'static str = "分类列表查询失败: {}";

    /// 清除分类列表查询缓存失败错误信息模板
    pub const cuowu_qingchu_fenlei_liebiao_huancun_shibai: &'static str = "清除分类列表查询缓存失败: {}";

    // ==================== 表名常量 ====================

    /// item_name表名
    pub const biao_ming_item_name: &'static str = "item_name";

    /// wupin_huizong表名
    pub const biao_ming_wupin_huizong: &'static str = "wupin_huizong";

    /// 指定字段查询支持表标识
    pub const biao_ming_zhiding_ziduan_zhichi: &'static str = "zhiding_ziduan_zhichi";

    // ==================== 工具方法 ====================

    /// 生成Redis键名 - 物品全部信息
    pub fn shengcheng_redis_jian_wupin_quanbu(wupin_id: &str) -> String {
        format!("{}:{}", Self::redis_jian_qianzhui_wupin_quanbu, wupin_id)
    }

    /// 生成物品不存在错误信息
    pub fn shengcheng_cuowu_wupin_bucunzai(wupin_id: &str) -> String {
        format!("物品ID {} 不存在", wupin_id)
    }

    /// 生成物品在汇总表中不存在错误信息
    pub fn shengcheng_cuowu_wupin_zai_huizong_biao_bucunzai(wupin_id: &str) -> String {
        format!("物品ID {} 在汇总表中不存在", wupin_id)
    }

    /// 生成物品全部信息缓存统计信息
    pub fn shengcheng_tongji_wupin_quanbu_xinxi_huancun(count: u64) -> String {
        format!("物品全部信息缓存: {} 个", count)
    }

    /// 生成分类子类型列表缓存统计信息
    pub fn shengcheng_tongji_fenlei_zileixing_huancun(count: u64) -> String {
        format!("物品分类子类型列表缓存: {} 个", count)
    }

    /// 生成分类缓存清除失败信息
    pub fn shengcheng_tongji_fenlei_huancun_qingchu_shibai(cuowu_xinxi: &str) -> String {
        format!("物品分类缓存清除失败: {}", cuowu_xinxi)
    }

    /// 生成查询类型列表失败错误信息
    pub fn shengcheng_cuowu_chaxun_leixing_liebiao_shibai(cuowu_xinxi: &str) -> String {
        format!("查询类型列表失败: {}", cuowu_xinxi)
    }

    /// 生成查询子类型列表失败错误信息
    pub fn shengcheng_cuowu_chaxun_zileixing_liebiao_shibai(cuowu_xinxi: &str) -> String {
        format!("查询子类型列表失败: {}", cuowu_xinxi)
    }

    /// 生成查询完整分类信息失败错误信息
    pub fn shengcheng_cuowu_chaxun_wanzheng_fenlei_shibai(cuowu_xinxi: &str) -> String {
        format!("查询完整分类信息失败: {}", cuowu_xinxi)
    }

    /// 生成查询分类统计信息失败错误信息
    pub fn shengcheng_cuowu_chaxun_fenlei_tongji_shibai(cuowu_xinxi: &str) -> String {
        format!("查询分类统计信息失败: {}", cuowu_xinxi)
    }

    /// 生成子类型Redis缓存键名
    pub fn shengcheng_redis_jian_fenlei_zileixing(leixing: &str) -> String {
        format!("{}:{}", Self::redis_jian_qianzhui_fenlei_zileixing, leixing)
    }

    /// 生成物品列表分页Redis缓存键名
    pub fn shengcheng_redis_jian_liebiao_fenye(yema: u32, meiye_shuliang: u32) -> String {
        format!("{}:{}:{}", Self::redis_jian_qianzhui_liebiao_fenye, yema, meiye_shuliang)
    }

    /// 生成物品名字查询Redis缓存键名
    pub fn shengcheng_redis_jian_mingzi_chaxun(chaxun_leixing: &str, chaxun_zhi: &str, yema: u32, meiye_shuliang: u32) -> String {
        format!("{}:{}:{}:{}:{}", Self::redis_jian_qianzhui_mingzi_chaxun, chaxun_leixing, chaxun_zhi, yema, meiye_shuliang)
    }

    /// 生成物品列表分页缓存统计信息
    pub fn shengcheng_tongji_liebiao_fenye_huancun(count: u64) -> String {
        format!("物品列表分页缓存: {} 个", count)
    }

    /// 生成列表缓存清除失败信息
    pub fn shengcheng_tongji_liebiao_huancun_qingchu_shibai(cuowu_xinxi: &str) -> String {
        format!("物品列表缓存清除失败: {}", cuowu_xinxi)
    }

    /// 生成查询物品列表失败错误信息
    pub fn shengcheng_cuowu_chaxun_liebiao_shibai(cuowu_xinxi: &str) -> String {
        format!("查询物品列表失败: {}", cuowu_xinxi)
    }

    /// 生成查询物品总数失败错误信息
    pub fn shengcheng_cuowu_chaxun_zongshu_shibai(cuowu_xinxi: &str) -> String {
        format!("查询物品总数失败: {}", cuowu_xinxi)
    }

    /// 生成清除分类缓存失败错误信息
    pub fn shengcheng_cuowu_qingchu_fenlei_huancun_shibai(cuowu_xinxi: &str) -> String {
        format!("清除分类缓存失败: {}", cuowu_xinxi)
    }

    /// 生成清除列表缓存失败错误信息
    pub fn shengcheng_cuowu_qingchu_liebiao_huancun_shibai(cuowu_xinxi: &str) -> String {
        format!("清除列表缓存失败: {}", cuowu_xinxi)
    }

    /// 生成物品名字查询缓存统计信息
    pub fn shengcheng_tongji_mingzi_chaxun_huancun(count: u64) -> String {
        format!("物品名字查询缓存: {} 个", count)
    }

    /// 生成名字查询缓存清除失败信息
    pub fn shengcheng_tongji_mingzi_chaxun_huancun_qingchu_shibai(cuowu_xinxi: &str) -> String {
        format!("物品名字查询缓存清除失败: {}", cuowu_xinxi)
    }

    /// 生成根据类名查询失败错误信息
    pub fn shengcheng_cuowu_chaxun_by_leiming_shibai(cuowu_xinxi: &str) -> String {
        format!("根据类名查询失败: {}", cuowu_xinxi)
    }

    /// 生成根据名字精确查询失败错误信息
    pub fn shengcheng_cuowu_chaxun_by_mingzi_jingque_shibai(cuowu_xinxi: &str) -> String {
        format!("根据名字精确查询失败: {}", cuowu_xinxi)
    }

    /// 生成根据名字模糊查询失败错误信息
    pub fn shengcheng_cuowu_chaxun_by_mingzi_mohu_shibai(cuowu_xinxi: &str) -> String {
        format!("根据名字模糊查询失败: {}", cuowu_xinxi)
    }

    /// 生成清除名字查询缓存失败错误信息
    pub fn shengcheng_cuowu_qingchu_mingzi_chaxun_huancun_shibai(cuowu_xinxi: &str) -> String {
        format!("清除名字查询缓存失败: {}", cuowu_xinxi)
    }

    /// 生成分类列表查询缓存统计信息
    pub fn shengcheng_tongji_fenlei_liebiao_huancun(count: u64) -> String {
        format!("物品分类列表查询缓存: {} 个", count)
    }

    /// 生成分类列表查询缓存清除失败信息
    pub fn shengcheng_tongji_fenlei_liebiao_huancun_qingchu_shibai(cuowu_xinxi: &str) -> String {
        format!("物品分类列表查询缓存清除失败: {}", cuowu_xinxi)
    }

    /// 生成分类列表查询失败错误信息
    pub fn shengcheng_cuowu_fenlei_liebiao_chaxun_shibai(cuowu_xinxi: &str) -> String {
        format!("分类列表查询失败: {}", cuowu_xinxi)
    }

    /// 生成清除分类列表查询缓存失败错误信息
    pub fn shengcheng_cuowu_qingchu_fenlei_liebiao_huancun_shibai(cuowu_xinxi: &str) -> String {
        format!("清除分类列表查询缓存失败: {}", cuowu_xinxi)
    }

    /// 生成Redis键名 - 分类列表查询
    pub fn shengcheng_redis_jian_fenlei_liebiao(huancun_jian_biaoshi: &str) -> String {
        format!("{}:{}", Self::redis_jian_qianzhui_fenlei_liebiao, huancun_jian_biaoshi)
    }

    /// 生成动态SQL查询语句 - item_name表指定字段
    pub fn shengcheng_sql_chaxun_jiben_biao_ziduan(ziduan_liebiao: &[&str]) -> String {
        let ziduan_str = ziduan_liebiao.join(", ");
        format!("SELECT {} FROM item_name WHERE ID = ?", ziduan_str)
    }

    /// 生成动态SQL查询语句 - wupin_huizong表指定字段
    pub fn shengcheng_sql_chaxun_huizong_biao_ziduan(ziduan_liebiao: &[&str]) -> String {
        let ziduan_str = ziduan_liebiao.join(", ");
        format!("SELECT {} FROM wupin_huizong WHERE wupin_id = ?", ziduan_str)
    }
}