#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use super::wupinjiegouti::wupin_wanzheng_xinxi;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use anyhow::Result;

/// 物品Redis缓存控制类
pub struct wupin_redis_kongzhi {
    redis_lianjie: redis_lianjie_guanli,
}

/// 物品缓存时间常量
impl wupin_redis_kongzhi {
    /// 物品全部信息缓存时间：3天（259200秒）
    pub const quanbu_xinxi_huancun_shijian: u64 = 259200;

    /// 物品分类信息缓存时间：30分钟（1800秒）
    pub const fenlei_xinxi_huancun_shijian: u64 = 1800;

    /// 物品列表信息缓存时间：5小时（18000秒）
    pub const liebiao_xinxi_huancun_shijian: u64 = 18000;
}

impl wupin_redis_kongzhi {
    /// 创建新的物品Redis控制实例
    pub fn new(redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            redis_lianjie,
        }
    }

    /// 生成物品全部信息的Redis键名
    fn shengcheng_quanbu_xinxi_jian(&self, wupin_id: &str) -> String {
        wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_wupin_quanbu(wupin_id)
    }

    /// 从Redis获取物品全部信息
    pub async fn huoqu_quanbu_xinxi(&self, wupin_id: &str) -> Result<Option<wupin_wanzheng_xinxi>> {
        let jian = self.shengcheng_quanbu_xinxi_jian(wupin_id);

        match self.redis_lianjie.huoqu(&jian).await? {
            Some(json_str) => {
                match serde_json::from_str::<wupin_wanzheng_xinxi>(&json_str) {
                    Ok(wupin_xinxi) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("从Redis成功获取物品{}的全部信息", wupin_id),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                        Ok(Some(wupin_xinxi))
                    },
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("物品{}的Redis缓存JSON解析失败: {}", wupin_id, e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                        Ok(None)
                    }
                }
            }
            None => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("物品{}在Redis中无缓存", wupin_id),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(None)
            }
        }
    }

    /// 将物品全部信息存储到Redis
    pub async fn cunchu_quanbu_xinxi(&self, wupin_id: &str, wupin_xinxi: &wupin_wanzheng_xinxi) -> Result<()> {
        let jian = self.shengcheng_quanbu_xinxi_jian(wupin_id);
        let json_str = serde_json::to_string(wupin_xinxi)?;

        match self.redis_lianjie.shezhi_with_guoqi(
            &jian,
            &json_str,
            Self::quanbu_xinxi_huancun_shijian as i64
        ).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("物品{}的全部信息已成功缓存到Redis，过期时间{}秒", wupin_id, Self::quanbu_xinxi_huancun_shijian),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("物品{}的全部信息缓存到Redis失败: {}", wupin_id, e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(e)
            }
        }
    }

    /// 删除指定物品的全部信息缓存
    pub async fn shanchu_quanbu_xinxi(&self, wupin_id: &str) -> Result<bool> {
        let jian = self.shengcheng_quanbu_xinxi_jian(wupin_id);
        match self.redis_lianjie.shanchu(&jian).await {
            Ok(deleted) => {
                if deleted {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        &format!("物品{}的全部信息缓存已从Redis删除", wupin_id),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                    );
                } else {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        &format!("物品{}在Redis中无缓存，无需删除", wupin_id),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                    );
                }
                Ok(deleted)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("删除物品{}的Redis缓存失败: {}", wupin_id, e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(e)
            }
        }
    }

    /// 清除所有物品全部信息的Redis缓存
    /// 只清除物品数据获取相关的缓存，不影响其他缓存
    pub async fn qingchu_wupin_quanbu_xinxi_huancun(&self) -> Result<u64> {
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "开始清除所有物品全部信息的Redis缓存...",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        match self.redis_lianjie.shanchu_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_wupin_quanbu).await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("成功清除{}个物品全部信息缓存", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(count)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除物品全部信息缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(e)
            }
        }
    }

    /// 获取物品缓存统计信息
    pub async fn huoqu_wupin_huancun_tongji(&self) -> Result<String> {
        let mut tongji_xinxi = Vec::new();

        // 统计物品全部信息缓存数量
        match self.redis_lianjie.count_keys_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_wupin_quanbu).await {
            Ok(count) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::shengcheng_tongji_wupin_quanbu_xinxi_huancun(count));
            }
            Err(_) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_wupin_quanbu_xinxi_huancun_shibai.to_string());
            }
        }

        Ok(tongji_xinxi.join("\n"))
    }

    /// 清除物品分类输出模块的缓存
    pub async fn qingchu_fenlei_huancun(&self) -> Result<()> {
        // 清除所有分类相关的缓存
        match self.redis_lianjie.shanchu_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_fenlei_suoyou).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    wupin_zifuchuan_changliangguanli::tongji_fenlei_huancun_qingchu_chenggong,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
            Err(e) => {
                let cuowu_xinxi = format!("{}", e);
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &wupin_zifuchuan_changliangguanli::shengcheng_tongji_fenlei_huancun_qingchu_shibai(&cuowu_xinxi),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_qingchu_fenlei_huancun_shibai(&e.to_string())))
            }
        }
    }

    /// 获取分类缓存统计信息
    pub async fn huoqu_fenlei_huancun_tongji(&self) -> Result<String> {
        let mut tongji_xinxi = Vec::new();

        // 统计类型列表缓存
        if let Ok(exists) = self.redis_lianjie.cunzai(wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_leixing).await {
            if exists {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_fenlei_leixing_yi_huancun.to_string());
            } else {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_fenlei_leixing_wei_huancun.to_string());
            }
        }

        // 统计子类型缓存数量
        let zileixing_pattern = format!("{}:*", wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_zileixing);
        match self.redis_lianjie.count_keys_by_pattern(&zileixing_pattern).await {
            Ok(count) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::shengcheng_tongji_fenlei_zileixing_huancun(count));
            }
            Err(_) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_fenlei_zileixing_huoqu_shibai.to_string());
            }
        }

        // 统计完整分类信息缓存
        if let Ok(exists) = self.redis_lianjie.cunzai(wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_wanzheng).await {
            if exists {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_fenlei_wanzheng_yi_huancun.to_string());
            } else {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_fenlei_wanzheng_wei_huancun.to_string());
            }
        }

        Ok(tongji_xinxi.join("\n"))
    }

    /// 清除物品列表模块的缓存
    pub async fn qingchu_liebiao_huancun(&self) -> Result<()> {
        // 清除所有列表相关的缓存
        match self.redis_lianjie.shanchu_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_liebiao_suoyou).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    wupin_zifuchuan_changliangguanli::tongji_liebiao_huancun_qingchu_chenggong,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
            Err(e) => {
                let cuowu_xinxi = format!("{}", e);
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &wupin_zifuchuan_changliangguanli::shengcheng_tongji_liebiao_huancun_qingchu_shibai(&cuowu_xinxi),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_qingchu_liebiao_huancun_shibai(&e.to_string())))
            }
        }
    }

    /// 获取列表缓存统计信息
    pub async fn huoqu_liebiao_huancun_tongji(&self) -> Result<String> {
        let mut tongji_xinxi = Vec::new();

        // 统计列表分页缓存数量
        let liebiao_pattern = format!("{}:*", wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_liebiao_fenye);
        match self.redis_lianjie.count_keys_by_pattern(&liebiao_pattern).await {
            Ok(count) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::shengcheng_tongji_liebiao_fenye_huancun(count));
            }
            Err(_) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_liebiao_fenye_huoqu_shibai.to_string());
            }
        }

        Ok(tongji_xinxi.join("\n"))
    }

    /// 清除物品名字查询模块的缓存
    pub async fn qingchu_mingzi_chaxun_huancun(&self) -> Result<()> {
        // 清除所有名字查询相关的缓存
        match self.redis_lianjie.shanchu_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_mingzi_chaxun_suoyou).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    wupin_zifuchuan_changliangguanli::tongji_mingzi_chaxun_huancun_qingchu_chenggong,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
            Err(e) => {
                let cuowu_xinxi = format!("{}", e);
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &wupin_zifuchuan_changliangguanli::shengcheng_tongji_mingzi_chaxun_huancun_qingchu_shibai(&cuowu_xinxi),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_qingchu_mingzi_chaxun_huancun_shibai(&e.to_string())))
            }
        }
    }

    /// 获取名字查询缓存统计信息
    pub async fn huoqu_mingzi_chaxun_huancun_tongji(&self) -> Result<String> {
        let mut tongji_xinxi = Vec::new();

        // 统计名字查询缓存数量
        let mingzi_chaxun_pattern = format!("{}:*", wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_mingzi_chaxun);
        match self.redis_lianjie.count_keys_by_pattern(&mingzi_chaxun_pattern).await {
            Ok(count) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::shengcheng_tongji_mingzi_chaxun_huancun(count));
            }
            Err(_) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_mingzi_chaxun_huoqu_shibai.to_string());
            }
        }

        Ok(tongji_xinxi.join("\n"))
    }

    /// 清除所有测试相关缓存（用于测试完成后清理）
    pub async fn qingchu_ceshi_huancun(&self) -> Result<()> {
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "开始清除测试相关缓存...",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        // 清除列表查询缓存
        let liebiao_pattern = format!("{}:*", wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_liebiao_fenye);
        match self.redis_lianjie.shanchu_by_pattern(&liebiao_pattern).await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("清除列表查询缓存成功，删除{}个键", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除列表查询缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 清除名字查询缓存
        let mingzi_pattern = format!("{}:*", wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_mingzi_chaxun);
        match self.redis_lianjie.shanchu_by_pattern(&mingzi_pattern).await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("清除名字查询缓存成功，删除{}个键", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除名字查询缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        // 清除分类列表查询缓存
        match self.redis_lianjie.shanchu_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_fenlei_liebiao_suoyou).await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("清除分类列表查询缓存成功，删除{}个键", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除分类列表查询缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
            }
        }

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试相关缓存清除完成！",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        Ok(())
    }

    /// 清除物品分类列表查询模块的缓存
    pub async fn qingchu_fenlei_liebiao_huancun(&self) -> Result<()> {
        // 清除所有分类列表查询相关的缓存
        match self.redis_lianjie.shanchu_by_pattern(wupin_zifuchuan_changliangguanli::redis_jian_moshi_fenlei_liebiao_suoyou).await {
            Ok(count) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("物品分类列表查询缓存清除成功，删除{}个键", count),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(())
            }
            Err(e) => {
                let cuowu_xinxi = format!("{}", e);
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &wupin_zifuchuan_changliangguanli::shengcheng_tongji_fenlei_liebiao_huancun_qingchu_shibai(&cuowu_xinxi),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(wupin_zifuchuan_changliangguanli::shengcheng_cuowu_qingchu_fenlei_liebiao_huancun_shibai(&e.to_string())))
            }
        }
    }

    /// 获取分类列表查询缓存统计信息
    pub async fn huoqu_fenlei_liebiao_huancun_tongji(&self) -> Result<String> {
        let mut tongji_xinxi = Vec::new();

        // 统计分类列表查询缓存数量
        let fenlei_liebiao_pattern = format!("{}:*", wupin_zifuchuan_changliangguanli::redis_jian_qianzhui_fenlei_liebiao);
        match self.redis_lianjie.count_keys_by_pattern(&fenlei_liebiao_pattern).await {
            Ok(count) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::shengcheng_tongji_fenlei_liebiao_huancun(count));
            }
            Err(_) => {
                tongji_xinxi.push(wupin_zifuchuan_changliangguanli::tongji_fenlei_liebiao_huoqu_shibai.to_string());
            }
        }

        Ok(tongji_xinxi.join("\n"))
    }
}