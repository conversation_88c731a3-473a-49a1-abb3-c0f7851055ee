#!/bin/bash

# 物品分类列表查询功能测试脚本

echo "🚀 开始测试物品分类列表查询功能..."
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "Cargo.toml" ]; then
    echo "❌ 错误: 请在houduan目录下运行此脚本"
    exit 1
fi

# 编译项目
echo "📦 编译项目..."
cargo build --release

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 运行测试
echo ""
echo "🧪 运行物品分类列表查询测试..."
echo "=================================="

./target/release/houduan test

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 测试完成！"
    echo "=================================="
    echo "✅ 物品分类列表查询功能测试通过"
    echo ""
    echo "测试包括："
    echo "  - 按类型查询物品列表"
    echo "  - 按类型和子类型查询物品列表"
    echo "  - 联合查询（类型+子类型+名字）"
    echo "  - 按子类型查询（不限制类型）"
    echo "  - 获取统计信息"
    echo "  - Redis缓存功能"
    echo ""
    echo "🔧 使用方法："
    echo "  ./houduan init  - 初始化系统"
    echo "  ./houduan test  - 运行测试"
    echo "  ./houduan       - 启动服务器"
else
    echo ""
    echo "❌ 测试失败"
    echo "请检查数据库连接和配置文件"
fi
